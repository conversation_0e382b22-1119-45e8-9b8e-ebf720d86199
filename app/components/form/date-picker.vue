<script setup lang="ts">
import type { CalendarDate } from '@internationalized/date';
import { getLocalTimeZone, today } from '@internationalized/date';
import { IconsLibrary } from 'assets/icons';

/* ---------------------------------------------------------------------------------------------- */

const model = defineModel<CalendarDate>({ default: today(getLocalTimeZone()) });
</script>

<template>
   <UPopover>
      <UButton color="neutral" variant="subtle" :icon="IconsLibrary.components.calendar">
         {{ model ? df.format(modelValue.toDate(getLocalTimeZone())) : 'Select a date' }}
      </UButton>

      <template #content>
         <UCalendar v-model="model" class="p-2" />
      </template>
   </UPopover>
</template>

<style scoped>

</style>
