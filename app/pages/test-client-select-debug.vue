<script setup lang="ts">
import SelectClient from '~/components/clients/select-client.vue';

const selectedClientId = ref('');
const selectedClient = ref();

// Test with a hardcoded client ID (you'll need to replace this with an actual client ID from your database)
const testClientId = ref('');

function setTestClientId() {
  // You can set this to an actual client ID from your database for testing
  testClientId.value = 'your-test-client-id-here';
  selectedClientId.value = testClientId.value;
}

function clearSelection() {
  selectedClientId.value = '';
  selectedClient.value = undefined;
}

// Watch for changes
watch(selectedClientId, (newValue) => {
  console.log('selectedClientId changed to:', newValue);
});

watch(selectedClient, (newValue) => {
  console.log('selectedClient changed to:', newValue);
});
</script>

<template>
  <DefaultLayout>
    <div class="container mx-auto p-8">
      <h1 class="text-2xl font-bold mb-6">Test Client Select Component - Debug</h1>
      
      <div class="max-w-md space-y-4">
        <div>
          <label class="block text-sm font-medium mb-2">Test Client ID Input:</label>
          <UInput 
            v-model="testClientId" 
            placeholder="Enter a client ID to test"
            class="mb-2"
          />
          <UButton @click="setTestClientId" class="mr-2">Set Client ID</UButton>
          <UButton @click="clearSelection" variant="outline">Clear Selection</UButton>
        </div>

        <div>
          <label class="block text-sm font-medium mb-2">Select Client Component:</label>
          <SelectClient 
            v-model:client="selectedClient"
            v-model:client-id="selectedClientId"
            name="client"
          />
        </div>
        
        <div class="mt-8 p-4 bg-gray-100 rounded-md">
          <h2 class="font-semibold mb-2">Selected Values:</h2>
          <p><strong>Client ID:</strong> {{ selectedClientId || 'None' }}</p>
          <p><strong>Client Object:</strong></p>
          <pre class="text-xs">{{ selectedClient || 'None' }}</pre>
        </div>

        <div class="mt-4 p-4 bg-blue-50 rounded-md">
          <h3 class="font-semibold mb-2">Instructions:</h3>
          <ol class="text-sm space-y-1">
            <li>1. Enter a valid client ID in the input above</li>
            <li>2. Click "Set Client ID" to test the functionality</li>
            <li>3. Check the browser console for debug logs</li>
            <li>4. The component should automatically fetch and display the client</li>
          </ol>
        </div>
      </div>
    </div>
  </DefaultLayout>
</template>
