<script setup lang="ts">
import type { FormSubmitEvent } from '#ui/types';
import type { BusinessClient } from '~/types/business-client';
import type { InvoiceFormItem, InvoiceStatus } from '~/types/invoice';
import { useSupabaseClient } from '#imports';
import { IconsLibrary } from 'assets/icons';
import * as v from 'valibot';
import SelectClient from '~/components/clients/select-client.vue';
import DatePicker from '~/components/form/date-picker.vue';
import SelectMenuInvoiceStatus from '~/components/form/select-menu-invoice-status.vue';
import { getBusinessClients } from '~/services/database/business-client-service';
import { createInvoiceWithItems } from '~/services/database/invoice-service';
import { useBusinessProfileStore } from '~/store/business-profile-store';

/* ---------------------------------------------------------------------------------------------- */

const businessProfileStore = useBusinessProfileStore();
const { businessProfileId } = storeToRefs(businessProfileStore);
const route = useRoute();
const toast = useToast();

/* ---------------------------------------------------------------------------------------------- */

const preselectedClientId = route.query.client as string | undefined;

/* ---------------------------------------------------------------------------------------------- */

// Form validation schema
const invoiceSchema = v.object({
   client_id: v.pipe(v.string(), v.minLength(1, 'Client is required')),
   issue_date: v.pipe(v.string(), v.minLength(1, 'Issue date is required')), // Changed from invoice_date
   due_date: v.pipe(v.string(), v.minLength(1, 'Due date is required')),
   status: v.pipe(
      v.string(),
      v.includes(['draft', 'sent', 'paid', 'overdue', 'cancelled'], 'Invalid status'),
   ),
   currency: v.optional(v.string()),
   notes: v.optional(v.string()),
});

type InvoiceSchema = v.InferOutput<typeof invoiceSchema>;

// Form state
const invoiceFormState = reactive({
   client_id: preselectedClientId || '',
   issue_date: new Date().toISOString().split('T')[0], // Today's date in YYYY-MM-DD format, changed from invoice_date
   due_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 30 days from now
   status: 'draft' as InvoiceStatus,
   currency: 'USD', // Default currency
   notes: '',
});

// Invoice items state
const invoiceItems = ref<InvoiceFormItem[]>([
   {
      description: '',
      quantity: 1,
      unit_price: 0,
      total_price: 0,
      isNew: true,
   },
]);

// Clients data
const clients = ref<BusinessClient[]>([]);
const isLoadingClients = ref(true);

// Form submission state
const isSubmitting = ref(false);
const formError = ref('');

/* ---------------------------------------------------------------------------------------------- */

// Status options for dropdown
const statusOptions = [
   { value: 'draft', label: 'Draft' },
   { value: 'sent', label: 'Sent' },
   { value: 'paid', label: 'Paid' },
   { value: 'overdue', label: 'Overdue' },
   { value: 'cancelled', label: 'Cancelled' },
];

// Computed total amount
const totalAmount = computed(() => {
   return invoiceItems.value.reduce((sum, item) => sum + item.total_price, 0);
});

/* ---------------------------------------------------------------------------------------------- */

// Fetch clients for the dropdown
async function fetchClients() {
   isLoadingClients.value = true;

   try {
      const supabase = useSupabaseClient();

      if (!businessProfileId.value) {
         throw new Error('Business profile not found');
      }

      clients.value = await getBusinessClients(supabase, businessProfileId.value, {
         activeOnly: true,
      });
   }
   catch (error: any) {
      console.error('Error fetching clients:', error);
      toast.add({
         title: 'Error',
         description: error.message || 'Failed to load clients',
         color: 'error',
      });
   }
   finally {
      isLoadingClients.value = false;
   }
}

// Add a new invoice item
function addInvoiceItem() {
   invoiceItems.value.push({
      description: '',
      quantity: 1,
      unit_price: 0,
      total_price: 0,
      isNew: true,
   });
}

// Remove an invoice item
function removeInvoiceItem(index: number) {
   invoiceItems.value.splice(index, 1);
}

// Update item total price when quantity or unit price changes
function updateItemTotal(item: InvoiceFormItem) {
   item.total_price = item.quantity * item.unit_price;
}

// Handle form submission
async function handleSubmit(event: FormSubmitEvent<InvoiceSchema>) {
   isSubmitting.value = true;
   formError.value = '';

   try {
      const supabase = useSupabaseClient();

      // Validate business profile ID
      if (!businessProfileId.value) {
         throw new Error('Business profile not found');
      }

      // Validate that we have at least one item
      if (invoiceItems.value.length === 0) {
         throw new Error('At least one invoice item is required');
      }

      // Validate that all items have a description and positive values
      for (const item of invoiceItems.value) {
         if (!item.description.trim()) {
            throw new Error('All invoice items must have a description');
         }
         if (item.quantity <= 0) {
            throw new Error('All invoice items must have a quantity greater than zero');
         }
         if (item.unit_price < 0) {
            throw new Error('All invoice items must have a unit price of zero or greater');
         }
      }

      // Create invoice with items
      await createInvoiceWithItems(
         supabase,
         {
            customer_id: businessProfileId.value, // Using businessProfileId as customer_id
            client_id: event.data.client_id,
            issue_date: event.data.issue_date,
            due_date: event.data.due_date,
            status: event.data.status as InvoiceStatus,
            currency: event.data.currency || 'USD',
            notes: event.data.notes || null,
            total: totalAmount.value,
         },
         invoiceItems.value.map(item => ({
            description: item.description,
            quantity: item.quantity,
            unit_price: item.unit_price,
            total_price: item.total_price,
         })),
      );

      toast.add({
         title: 'Invoice Created',
         description: 'Invoice has been created successfully',
         color: 'success',
      });

      // Navigate to invoices list
      navigateTo('/invoices');
   }
   catch (error: any) {
      console.error('Error creating invoice:', error);
      formError.value = error.message || 'Failed to create invoice. Please try again.';

      toast.add({
         title: 'Error',
         description: error.message || 'There was a problem creating the invoice',
         color: 'error',
      });
   }
   finally {
      isSubmitting.value = false;
   }
}
</script>

<template>
   <DefaultLayout>
      <div class="space-y-6">
         <!-- Header -->
         <header class="flex items-center gap-4">
            <UButton
               variant="soft"
               color="neutral"
               to="/invoices"
               size="sm"
            >
               <template #leading>
                  <UIcon :name="IconsLibrary.actions.back" />
               </template>
               Back to Invoices
            </UButton>

            <h1 class="text-2xl font-bold">
               Create New Invoice
            </h1>
         </header>

         <!-- region: debug -->
         <div />
         <!-- endregion: debug -->

         <!-- Invoice form -->
         <UCard class="max-w-4xl">
            <UForm
               :schema="invoiceSchema"
               :state="invoiceFormState"
               class="flex flex-col gap-6 w-full"
               @submit="handleSubmit"
            >
               <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
                  <SelectClient v-model:client-id="preselectedClientId" name="client_id" />
               </div>

               <!-- Client and Dates Section -->
               <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <UFormField label="Status" name="status" required>
                     <SelectMenuInvoiceStatus
                        v-model="invoiceFormState.status"
                        placeholder="Select status"
                        class="w-full"
                     />
                  </UFormField>

                  <UFormField label="Issue Date" name="issue_date" required>
                     <DatePicker
                        :date="invoiceFormState.issue_date"
                        class="w-full"
                     />
                  </UFormField>

                  <UFormField label="Due Date" name="due_date" required>
                     <UInput
                        v-model="invoiceFormState.due_date"
                        type="date"
                        class="w-full"
                     />
                  </UFormField>

                  <UFormField label="Currency" name="currency">
                     <UInput
                        v-model="invoiceFormState.currency"
                        placeholder="USD"
                        class="w-full"
                     />
                  </UFormField>
               </div>

               <!-- Invoice Items Section -->
               <div class="mt-6">
                  <div class="flex justify-between items-center mb-4">
                     <h3 class="text-lg font-medium">
                        Invoice Items
                     </h3>
                     <UButton
                        color="primary"
                        variant="soft"
                        size="sm"
                        @click="addInvoiceItem"
                     >
                        <template #leading>
                           <UIcon name="i-heroicons-plus" />
                        </template>
                        Add Item
                     </UButton>
                  </div>

                  <!-- Items Table -->
                  <div class="overflow-x-auto">
                     <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                           <tr>
                              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                 Description
                              </th>
                              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                 Quantity
                              </th>
                              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                 Unit Price
                              </th>
                              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                 Total
                              </th>
                              <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                 Actions
                              </th>
                           </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                           <tr v-for="(item, index) in invoiceItems" :key="index" class="hover:bg-gray-50">
                              <td class="px-6 py-4 whitespace-nowrap">
                                 <UInput
                                    v-model="item.description"
                                    placeholder="Item description"
                                    class="w-full"
                                 />
                              </td>
                              <td class="px-6 py-4 whitespace-nowrap">
                                 <UInput
                                    v-model="item.quantity"
                                    type="number"
                                    min="1"
                                    step="1"
                                    class="w-24"
                                    @update:model-value="updateItemTotal(item)"
                                 />
                              </td>
                              <td class="px-6 py-4 whitespace-nowrap">
                                 <UInput
                                    v-model="item.unit_price"
                                    type="number"
                                    min="0"
                                    step="0.01"
                                    class="w-32"
                                    @update:model-value="updateItemTotal(item)"
                                 />
                              </td>
                              <td class="px-6 py-4 whitespace-nowrap">
                                 <div class="text-sm font-medium text-gray-900">
                                    {{ new Intl.NumberFormat('en-US', { style: 'currency', currency: invoiceFormState.currency || 'USD' }).format(item.total_price) }}
                                 </div>
                              </td>
                              <td class="px-6 py-4 whitespace-nowrap text-right">
                                 <UButton
                                    v-if="invoiceItems.length > 1"
                                    color="error"
                                    variant="ghost"
                                    size="sm"
                                    icon="i-heroicons-trash"
                                    @click="removeInvoiceItem(index)"
                                 />
                              </td>
                           </tr>
                        </tbody>
                        <tfoot>
                           <tr>
                              <td colspan="3" class="px-6 py-4 text-right font-medium">
                                 Total Amount:
                              </td>
                              <td class="px-6 py-4 whitespace-nowrap font-bold text-lg">
                                 {{ new Intl.NumberFormat('en-US', { style: 'currency', currency: invoiceFormState.currency || 'USD' }).format(totalAmount) }}
                              </td>
                              <td />
                           </tr>
                        </tfoot>
                     </table>
                  </div>
               </div>

               <!-- Notes Section -->
               <div class="mt-6">
                  <UFormField label="Notes" name="notes">
                     <UTextarea
                        v-model="invoiceFormState.notes"
                        placeholder="Additional notes or payment instructions"
                        :rows="3"
                        class="w-full"
                     />
                  </UFormField>
               </div>

               <!-- Error Alert -->
               <div v-if="formError" class="mt-4">
                  <UAlert
                     color="error"
                     variant="soft"
                     title="Error"
                     :description="formError"
                  />
               </div>

               <!-- Form Actions -->
               <div class="flex justify-end gap-3 pt-4">
                  <UButton
                     type="button"
                     variant="outline"
                     color="neutral"
                     to="/invoices"
                  >
                     Cancel
                  </UButton>

                  <UButton
                     type="submit"
                     color="primary"
                     :loading="isSubmitting"
                  >
                     Create Invoice
                  </UButton>
               </div>
            </UForm>
         </UCard>
      </div>
   </DefaultLayout>
</template>
